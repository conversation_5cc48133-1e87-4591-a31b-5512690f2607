# 开发环境配置

## 本地开发环境

需要安装 nodejs, 推荐使用 nvm 来安装，可以方便的在不同的 nodejs 版本之间切换, 也可以直接安装 nodejs 的 22.11.0 版本

### 安装 nvm

-   Mac/Linux: https://github.com/nvm-sh/nvm
-   Windows: https://github.com/coreybutler/nvm-windows

### 通过 nvm 安装 nodejs 22.11.0 版本

```
### 安装 nodejs 22.11.0 版本
nvm install 22.11.0

```

### 使用 npm 安装 yarn 和 husky 全局依赖

```
npm i yarn -g

npm i husky -g
```

### 到 Gitlab 里面添加自己开发机器的 ssh keys (如果之前已经添加过可以忽略该步骤)

-   https://gitlab.stnts.com/profile/keys

To add an SSH key you need to [generate one](https://gitlab.stnts.com/help/ssh/README#generating-a-new-ssh-key-pair) or use an [existing key](https://gitlab.stnts.com/help/ssh/README#locating-an-existing-ssh-key-pair).

### 进入项目根目录，然后安装项目依赖

```
yarn install --ingore-optional
```

### 安装 Cocos Creator Dahsboard ，然后通过 Dashboard 安装 3.8.4 版本的编辑器

https://docs.cocos.com/creator/3.8/manual/zh/getting-started/install/

### 使用 Cocos Creator Dashboard 导入安装完依赖的项目, 路径为 packages/game-shark

### 在场景中选中 start 场景并点击预览，即可启动浏览器打开游戏进行本地开发预览的页面

## 联调及测试环境

### 环境配置

-   hosts (测试路由器 st-csb-suileyoo-testing 已添加 hosts)

```
# 联调环境hosts
********** cocos-game-dev.mityoo.com
*********** game-server-dev.mityoo.com

#测试环境hosts
********** cocos-game-testing.mityoo.com
```

### 访问地址

#### 联调环境访问地址

```
# 服务端创建对局
https://cocos-game-dev.mityoo.com/dashboard/#/

# 客户端游戏地址
https://cocos-game-dev.mityoo.com/shark/web-mobile/
```

#### 测试环境访问地址

```
# 客户端游戏地址
https://cocos-game-testing.mityoo.com/shark/web-mobile/
```

### 发布流程

CI 运行完打包流程后，手动触发 deploy_test 的 pipeline 即可将打包产物部署到测试环境机器

### 后台创建对局（以联调环境为例）

-   打开创建对局的页面 https://cocos-game-dev.mityoo.com/dashboard/#/
-   点击右下角按钮弹出创建对局表单,需要填写下面的表单项，其它表单项保持默认就行

```
Web服务器：
https://game-server-dev.mityoo.com/

连接器地址：
wss://game-server-dev.mityoo.com/ws/conn

预览地址（这里如果想在本地调试，可以换成本地的localhost + 端口的地址）：
https://cocos-game-dev.mityoo.com/shark/web-mobile/

游戏ID:
care_shark

游戏模式：
竞技模式-个人

游戏Feature:
suileyoo

队伍数量：
2-6

高级设置-safe_top(顶部安全区域高度):
100

高级设置-safe_bottom(底部安全区域高度):
750

高级设置-额外字段:
{
  "state_notify": "",
  "report_game": "",
  "ranking_settlement": ""
}
```

-   点击创建按钮
-   页面上会针对填写的队伍数量生成多个对战项目，点击加入对战右侧的复制按钮可以复制地址到浏览器打开参与对战

#### 通过高级设置-额外字段定制玩家手牌和初始牌堆

额外字段参考：

```
{
  "state_notify": "",
  "report_game": "",
  "ranking_settlement": "",
  "deck_cards": "landmine,landmine,landmine,landmine,landmine",
  "player_cards.0": "remove,remove,curse,curse,curse,curse",
  "player_cards.1": "remove,remove,remove,remove,remove,remove"
}
```

卡牌映射关系

```
{
	"landmine": "地雷",
	"prediction": "探雷",
	"scout": "诊断",
	"give": "索取",
	"exchange": "交换",
	"shuffle": "洗牌",
	"turn": "转向",
	"throw": "甩锅",
	"pass": "跳过",
	"pass2": "跳过2",
	"remove": "逃生",
	"resistance": "休想",
	"curse": "诅咒",
	"clamp": "钳子",
	"blessing": "祈祷",
}
```
